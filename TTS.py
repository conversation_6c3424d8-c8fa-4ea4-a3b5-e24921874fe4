from tortoise.api import TextToSpeech
from tortoise.utils.audio import load_audio, get_voices

tts = TextToSpeech()
# point to your 10-minute clip here
voice_samples, conditioning_latents = load_audio("myvoice.wav")

out = tts.tts_with_preset(
    "This is my cloned voice speaking clearly.",
    voice_samples=[voice_samples],
    conditioning_latents=conditioning_latents,
    preset="fast"
)

tts.save_wav(out, "clone_output.wav")
